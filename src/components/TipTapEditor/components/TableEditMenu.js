import React, { useState, useRef, useEffect } from 'react';
import './TableEditMenu.css';

const TableEditMenu = ({ editor, tableNode, position, onClose }) => {
  const [isVisible, setIsVisible] = useState(true);
  const menuRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  const commands = {
    // 行操作
    addRowBefore: () => {
      editor.chain().focus().addRowBefore().run();
      onClose();
    },
    addRowAfter: () => {
      editor.chain().focus().addRowAfter().run();
      onClose();
    },
    deleteRow: () => {
      editor.chain().focus().deleteRow().run();
      onClose();
    },

    // 列操作
    addColumnBefore: () => {
      editor.chain().focus().addColumnBefore().run();
      onClose();
    },
    addColumnAfter: () => {
      editor.chain().focus().addColumnAfter().run();
      onClose();
    },
    deleteColumn: () => {
      editor.chain().focus().deleteColumn().run();
      onClose();
    },

    // 表格操作
    deleteTable: () => {
      editor.chain().focus().deleteTable().run();
      onClose();
    },

  };

  if (!isVisible) return null;

  return (
    <div
      ref={menuRef}
      className="table-edit-menu"
      style={{
        position: 'absolute',
        top: position.top,
        left: position.left,
        zIndex: 1000,
      }}
    >
      <div className="table-edit-menu-header">
        <span>表格编辑</span>
        <button className="close-button" onClick={onClose}>
          ×
        </button>
      </div>

      <div className="table-edit-menu-content">
        {/* 行操作 */}
        <div className="menu-section">
          <div className="menu-section-title">行操作</div>
          <button className="menu-item" onClick={commands.addRowBefore}>
            <span className="menu-icon">⬆️</span>
            在上方插入行
          </button>
          <button className="menu-item" onClick={commands.addRowAfter}>
            <span className="menu-icon">⬇️</span>
            在下方插入行
          </button>
          <button className="menu-item danger" onClick={commands.deleteRow}>
            <span className="menu-icon">🗑️</span>
            删除当前行
          </button>
        </div>

        {/* 列操作 */}
        <div className="menu-section">
          <div className="menu-section-title">列操作</div>
          <button className="menu-item" onClick={commands.addColumnBefore}>
            <span className="menu-icon">⬅️</span>
            在左侧插入列
          </button>
          <button className="menu-item" onClick={commands.addColumnAfter}>
            <span className="menu-icon">➡️</span>
            在右侧插入列
          </button>
          <button className="menu-item danger" onClick={commands.deleteColumn}>
            <span className="menu-icon">🗑️</span>
            删除当前列
          </button>
        </div>

        {/* 表格操作 */}
        <div className="menu-section">
          <div className="menu-section-title">表格操作</div>
          <button className="menu-item danger" onClick={commands.deleteTable}>
            <span className="menu-icon">🗑️</span>
            删除整个表格
          </button>
        </div>
      </div>
    </div>
  );
};

export default TableEditMenu;
